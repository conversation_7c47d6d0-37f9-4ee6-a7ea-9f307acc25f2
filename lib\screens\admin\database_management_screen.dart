import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/database_table_model.dart';
import '../../utils/responsive_helper.dart';
import '../widgets/common/loading_indicator.dart';
import '../widgets/common/error_view.dart';
import 'database_table_view.dart';
import 'task_database_enhancements.dart';

/// شاشة إدارة قاعدة البيانات
///
/// توفر واجهة شاملة لإدارة جداول قاعدة البيانات
class DatabaseManagementScreen extends StatefulWidget {
  const DatabaseManagementScreen({super.key});

  @override
  State<DatabaseManagementScreen> createState() => _DatabaseManagementScreenState();
}

class _DatabaseManagementScreenState extends State<DatabaseManagementScreen> {
  @override
  void initState() {
    super.initState();
    // تأكد من تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final controller = Get.find<DatabaseManagementController>();

      // التحقق من حالة تسجيل الدخول أولاً
      final authController = Get.find<AuthController>();
      if (authController.isLoggedIn) {
        controller.refresh();
      } else {
        // إذا لم يكن المستخدم مسجل دخول، اعرض رسالة
        Get.snackbar(
          'تسجيل الدخول مطلوب',
          'يرجى تسجيل الدخول أولاً للوصول إلى إدارة قاعدة البيانات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange.shade100,
          colorText: Colors.orange.shade800,
          duration: const Duration(seconds: 3),
        );
      }
    });
  }



  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DatabaseManagementController>();
    final isDesktop = ResponsiveHelper.isDesktop(context);

    return Container(
      color: AppColors.background,
      child: Column(
        children: [
          _buildHeader(controller),
          Expanded(
            child: Obx(() {
              // عرض رسالة خطأ إذا كان هناك خطأ في الاتصال
              if (controller.error.isNotEmpty && controller.tables.isEmpty) {
                return Center(
                  child: ErrorView(
                    message: controller.error,
                    onRetry: () => controller.refresh(),
                  ),
                );
              }

              // عرض مؤشر التحميل إذا كان يتم تحميل الجداول
              if (controller.isLoadingTables && controller.tables.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تحميل الجداول...'),
                    ],
                  ),
                );
              }

              return isDesktop
                  ? _buildDesktopLayout(controller)
                  : _buildMobileLayout(controller);
            }),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الشاشة
  Widget _buildHeader(DatabaseManagementController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.storage,
            size: 32,
            color: AppColors.primary,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة قاعدة البيانات',
                  style: AppStyles.headlineMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Obx(() => Text(
                  controller.isLoading 
                      ? 'جاري التحميل...'
                      : 'إدارة وعرض جداول قاعدة البيانات',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                )),
              ],
            ),
          ),
          _buildConnectionStatus(controller),
          const SizedBox(width: 16),
          // أزرار التحسينات الخاصة بالمهام
          ...TaskDatabaseEnhancements.getTaskTableActions(
            context,
            controller,
            controller.selectedTable ?? DatabaseTable(
              name: '',
              displayName: '',
              columns: [],
              recordCount: 0,
            ),
          ),
          if (TaskDatabaseEnhancements.getTaskTableActions(
            context,
            controller,
            controller.selectedTable ?? DatabaseTable(
              name: '',
              displayName: '',
              columns: [],
              recordCount: 0,
            ),
          ).isNotEmpty) const SizedBox(width: 8),
          _buildRefreshButton(controller),
        ],
      ),
    );
  }

  /// بناء حالة الاتصال
  Widget _buildConnectionStatus(DatabaseManagementController controller) {
    return Obx(() {
      final isConnected = controller.databaseInfo.isNotEmpty;
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isConnected ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isConnected ? Colors.green : Colors.red,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isConnected ? Icons.check_circle : Icons.error,
              size: 16,
              color: isConnected ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 6),
            Text(
              isConnected ? 'متصل' : 'غير متصل',
              style: AppStyles.bodySmall.copyWith(
                color: isConnected ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء زر التحديث
  Widget _buildRefreshButton(DatabaseManagementController controller) {
    return Obx(() => IconButton(
      onPressed: controller.isLoading ? null : () => controller.refresh(),
      icon: controller.isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.refresh),
      tooltip: 'تحديث البيانات',
    ));
  }

  /// بناء تخطيط سطح المكتب
  Widget _buildDesktopLayout(DatabaseManagementController controller) {
    return Row(
      children: [
        // قائمة الجداول
        SizedBox(
          width: 300,
          child: _buildTablesList(controller),
        ),
        // خط فاصل
        Container(
          width: 1,
          color: AppColors.border,
        ),
        // عرض الجدول
        Expanded(
          child: _buildTableView(controller),
        ),
      ],
    );
  }

  /// بناء تخطيط الهاتف المحمول
  Widget _buildMobileLayout(DatabaseManagementController controller) {
    return Obx(() {
      if (controller.selectedTable == null) {
        return _buildTablesList(controller);
      } else {
        return _buildTableView(controller);
      }
    });
  }

  /// بناء قائمة الجداول
  Widget _buildTablesList(DatabaseManagementController controller) {
    return Container(
      color: AppColors.surface,
      child: Column(
        children: [
          // رأس قائمة الجداول
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.border),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.table_chart,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'الجداول المتاحة',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Obx(() => Text(
                  '${controller.tables.length}',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                )),
              ],
            ),
          ),
          // قائمة الجداول
          Expanded(
            child: Obx(() {
              if (controller.isLoadingTables) {
                return const Center(child: LoadingIndicator());
              }

              if (controller.tables.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.table_chart_outlined,
                        size: 64,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد جداول متاحة',
                        style: AppStyles.bodyLarge.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'تحقق من الاتصال بقاعدة البيانات',
                        style: AppStyles.bodyMedium.copyWith(
                          color: AppColors.textHint,
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton.icon(
                        onPressed: () => controller.refresh(),
                        icon: const Icon(Icons.refresh),
                        label: const Text('إعادة المحاولة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                itemCount: controller.tables.length,
                itemBuilder: (context, index) {
                  final table = controller.tables[index];
                  return _buildTableListItem(controller, table);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  /// بناء عنصر في قائمة الجداول
  Widget _buildTableListItem(DatabaseManagementController controller, DatabaseTable table) {
    return Obx(() {
      final isSelected = controller.selectedTable?.name == table.name;

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : null,
          borderRadius: BorderRadius.circular(8),
          border: isSelected ? Border.all(color: AppColors.primary) : null,
        ),
        child: ListTile(
          leading: Icon(
            TaskDatabaseEnhancements.getTableIcon(table.name) ?? Icons.table_view,
            color: isSelected
                ? (TaskDatabaseEnhancements.getTableColor(table.name) ?? AppColors.primary)
                : AppColors.textSecondary,
          ),
          title: Text(
            table.displayName,
            style: AppStyles.bodyMedium.copyWith(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? AppColors.primary : AppColors.textPrimary,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                table.name,
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              if (table.description != null && table.description!.isNotEmpty)
                Text(
                  table.description!,
                  style: AppStyles.captionText.copyWith(
                    color: AppColors.textHint,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              // إضافة تلميح خاص بالمهام
              if (TaskDatabaseEnhancements.getTaskTableHint(table.name) != null)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    TaskDatabaseEnhancements.getTaskTableHint(table.name)!,
                    style: AppStyles.captionText.copyWith(
                      color: TaskDatabaseEnhancements.getTableColor(table.name) ?? AppColors.primary,
                      fontStyle: FontStyle.italic,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
            ],
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${table.columns.length} عمود',
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              if (table.recordCount > 0)
                Text(
                  '${table.recordCount} سجل',
                  style: AppStyles.captionText.copyWith(
                    color: AppColors.textHint,
                  ),
                ),
            ],
          ),
          onTap: () => controller.selectTable(table),
        ),
      );
    });
  }

  /// بناء عرض الجدول
  Widget _buildTableView(DatabaseManagementController controller) {
    return Obx(() {
      if (controller.selectedTable == null) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.table_chart_outlined,
                size: 64,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: 16),
              Text(
                'اختر جدولاً لعرض بياناته',
                style: AppStyles.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        );
      }

      if (controller.error.isNotEmpty) {
        return Center(
          child: ErrorView(
            message: controller.error,
            onRetry: () => controller.loadTableData(),
          ),
        );
      }

      return DatabaseTableView(
        controller: controller,
        table: controller.selectedTable!,
      );
    });
  }
}
