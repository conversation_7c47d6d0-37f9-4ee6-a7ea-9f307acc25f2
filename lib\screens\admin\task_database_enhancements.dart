import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/database_management_controller.dart';
import '../../controllers/task_controller.dart';
import '../../models/database_table_model.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// تحسينات خاصة لإدارة المهام في قاعدة البيانات
///
/// يوفر واجهات وأدوات محسنة للتعامل مع جداول المهام
class TaskDatabaseEnhancements {
  static final TaskController _taskController = Get.find<TaskController>();

  /// إضافة أزرار إجراءات خاصة بالمهام
  static List<Widget> getTaskTableActions(
    BuildContext context,
    DatabaseManagementController controller,
    DatabaseTable table,
  ) {
    if (!_isTaskRelatedTable(table.name)) {
      return [];
    }

    return [
      // زر إحصائيات المهام
      if (table.name.toLowerCase() == 'tasks')
        IconButton(
          onPressed: () => _showTaskStatistics(context),
          icon: const Icon(Icons.analytics),
          tooltip: 'إحصائيات المهام',
        ),

      // زر إدارة سريعة للحالات
      if (table.name.toLowerCase() == 'taskstatuses')
        IconButton(
          onPressed: () => _showQuickStatusManager(context),
          icon: const Icon(Icons.speed),
          tooltip: 'إدارة سريعة للحالات',
        ),

      // زر إدارة سريعة للأولويات
      if (table.name.toLowerCase() == 'taskpriorities')
        IconButton(
          onPressed: () => _showQuickPriorityManager(context),
          icon: const Icon(Icons.priority_high),
          tooltip: 'إدارة سريعة للأولويات',
        ),

      // زر إدارة سريعة للأنواع
      if (table.name.toLowerCase() == 'tasktypes')
        IconButton(
          onPressed: () => _showQuickTypeManager(context),
          icon: const Icon(Icons.category),
          tooltip: 'إدارة سريعة للأنواع',
        ),
    ];
  }

  /// التحقق من أن الجدول متعلق بالمهام
  static bool _isTaskRelatedTable(String tableName) {
    final lowerName = tableName.toLowerCase();
    return lowerName.startsWith('task') || lowerName == 'subtasks';
  }

  /// عرض إحصائيات المهام
  static void _showTaskStatistics(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.analytics, color: AppColors.primary),
            const SizedBox(width: 8),
            const Text('إحصائيات المهام'),
          ],
        ),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Obx(() => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatCard(
                'إجمالي المهام',
                '${_taskController.allTasks.length}',
                Icons.task_alt,
                AppColors.primary,
              ),
              const SizedBox(height: 12),
              _buildStatCard(
                'المهام المكتملة',
                '${_taskController.allTasks.where((t) => t.status == 4).length}',
                Icons.check_circle,
                Colors.green,
              ),
              const SizedBox(height: 12),
              _buildStatCard(
                'المهام قيد التنفيذ',
                '${_taskController.allTasks.where((t) => t.status == 2).length}',
                Icons.pending,
                Colors.orange,
              ),
              const SizedBox(height: 12),
              _buildStatCard(
                'المهام المتأخرة',
                '${_getOverdueTasks()}',
                Icons.warning,
                Colors.red,
              ),
            ],
          )),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // فتح تقرير مفصل
              Get.snackbar(
                'قيد التطوير',
                'سيتم إضافة التقرير المفصل قريباً',
                snackPosition: SnackPosition.BOTTOM,
              );
            },
            child: const Text('تقرير مفصل'),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  static Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppStyles.bodyMedium.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  value,
                  style: AppStyles.headingMedium.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// حساب المهام المتأخرة
  static int _getOverdueTasks() {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return _taskController.allTasks
        .where((task) => 
          task.dueDate != null && 
          task.dueDate! < now && 
          task.status != 4) // ليست مكتملة
        .length;
  }

  /// عرض مدير سريع للحالات
  static void _showQuickStatusManager(BuildContext context) {
    Get.snackbar(
      'قيد التطوير',
      'سيتم إضافة مدير الحالات السريع قريباً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColors.warning,
      colorText: Colors.white,
    );
  }

  /// عرض مدير سريع للأولويات
  static void _showQuickPriorityManager(BuildContext context) {
    Get.snackbar(
      'قيد التطوير',
      'سيتم إضافة مدير الأولويات السريع قريباً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColors.warning,
      colorText: Colors.white,
    );
  }

  /// عرض مدير سريع للأنواع
  static void _showQuickTypeManager(BuildContext context) {
    Get.snackbar(
      'قيد التطوير',
      'سيتم إضافة مدير الأنواع السريع قريباً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColors.warning,
      colorText: Colors.white,
    );
  }

  /// الحصول على لون مخصص للجدول
  static Color? getTableColor(String tableName) {
    switch (tableName.toLowerCase()) {
      case 'tasks':
        return AppColors.primary;
      case 'taskstatuses':
        return Colors.blue;
      case 'taskpriorities':
        return Colors.orange;
      case 'tasktypes':
        return Colors.purple;
      case 'taskcomments':
        return Colors.green;
      case 'taskhistories':
        return Colors.grey;
      case 'subtasks':
        return Colors.teal;
      default:
        return null;
    }
  }

  /// الحصول على أيقونة مخصصة للجدول
  static IconData? getTableIcon(String tableName) {
    switch (tableName.toLowerCase()) {
      case 'tasks':
        return Icons.task_alt;
      case 'taskstatuses':
        return Icons.assignment_turned_in;
      case 'taskpriorities':
        return Icons.priority_high;
      case 'tasktypes':
        return Icons.category;
      case 'taskcomments':
        return Icons.comment;
      case 'taskhistories':
        return Icons.history;
      case 'subtasks':
        return Icons.subdirectory_arrow_right;
      default:
        return null;
    }
  }

  /// إضافة تلميحات مفيدة للمهام
  static String? getTaskTableHint(String tableName) {
    switch (tableName.toLowerCase()) {
      case 'tasks':
        return 'يمكنك إنشاء وتعديل المهام، تعيين المسؤولين، وتتبع التقدم من هنا';
      case 'taskstatuses':
        return 'إدارة حالات المهام: جديدة، قيد التنفيذ، مكتملة، ملغاة';
      case 'taskpriorities':
        return 'تحديد مستويات الأولوية: منخفضة، متوسطة، عالية، عاجلة';
      case 'tasktypes':
        return 'تصنيف المهام حسب النوع: مشاريع، مهام يومية، طوارئ';
      case 'taskcomments':
        return 'عرض وإدارة تعليقات المستخدمين على المهام';
      case 'taskhistories':
        return 'تتبع جميع التغييرات والتحديثات على المهام';
      case 'subtasks':
        return 'إدارة المهام الفرعية والمهام المتفرعة';
      default:
        return null;
    }
  }
}
